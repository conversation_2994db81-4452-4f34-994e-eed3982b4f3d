# 个人简历

## 教育背景

- **萨塞克斯大学- 人工智能与自适应系统专业-硕士**  2024年9月 – 2026年3月
  - **核心课程：**图像处理、自然语言处理、机器学习、动物与机器智能、智能系统技术、Python编程
  - **毕业论文方向：**基于 U-Net + VAE 的医学图像异常检测模型研究，关注模型不确定性估计与实际临床落地的适配性
- **烟台大学-智能科学与技术专业- 学士**  2020年9月 – 2024年6月
  - **平均绩点：** 3.21/5.0（83.84/100）
  - **毕业论文：**基于深度学习的用户表情识别系统
  - **相关课程：**数据结构与算法、模式识别、图像处理、高级编程语言（R语言）、智能机器人、问题解决策略
  - **校内经历：**担任蓝翼青年志愿者协会副会长，校内校外组织开展数十次志愿服务活动

## 实习经历

**山东诺瑞特智能科技有限公司**  **软件技术开发**  2023.07 - 2023.08

- 参与公司产品智能热水器控制器的软件开发
- 主动提出并设计语音控制功能原型，集成语音识别API实现基本的语音指令解析，构建关键词匹配算法识别开关、温度调节等控制命令，并设计了简单的语音反馈机制，有效提升人机交互效率
- 协助撰写项目功能需求文档并参与方案评审，提升团队软硬件协同能力

## 项目经历与获奖情况

**1.Kaggle竞赛Image Matching Challenge 2025**  获得银牌区成绩

- 学习并应用深度学习图像匹配技术，使用预训练的特征提取模型和匹配算法，实现图像关键点检测与配准功能
- 通过调研和实践多种图像匹配方法，包括全局特征提取和局部描述子技术，深入理解计算机视觉在图像理解中的应用
- 项目加深了对AI视觉算法的理解，了解了相关技术在AR导航、自动驾驶等领域的应用潜力

  **2.Kaggle竞赛Yale/UNC-CH - Geophysical Waveform Inversion**  获得银牌区成绩
- 学习并实践UNet深度学习架构，通过调整网络结构和参数配置，提升模型在图像分割任务中的表现
- 探索多种数据增强技术，包括图像翻转、随机裁剪和噪声添加等方法，有效改善模型的泛化能力
- 掌握了混合精度训练和梯度优化技术，学会在有限计算资源下提升模型训练效率和稳定性

  **3.Kaggle竞赛Predict Podcast Listening Time**  获得前5%成绩
- 完成用户行为预测的回归任务，学习处理包含缺失值和类别变量的真实数据集，掌握数据清洗和特征工程的基本方法
- 实践多种机器学习算法，重点使用CatBoost和LightGBM等梯度提升模型，通过交叉验证和参数调优提升模型性能
- 学会模型集成策略，通过组合多个模型的预测结果提高最终准确率，获得了宝贵的机器学习竞赛经验

  **4.基于深度学习的车牌识别系统**
- 使用PyTorch框架构建CNN卷积神经网络模型，实现中国车牌字符的精准识别，模型包含多层卷积层、批归一化层和池化层，有效提取车牌图像特征
- 设计数据预处理流程，包括图像尺寸统一化(224×224)、张量转换和标准化，提高模型对不同光照和角度下车牌图像的适应性
- 实现自定义数据集类，支持高效批量处理和数据增强，采用交叉熵损失函数和Adam优化器，通过30轮迭代训练将损失从0.07降至0.025
- 应用批归一化技术提升模型收敛速度和稳定性，最终模型在测试集上展现出良好的泛化能力，可应用于智能交通和安防监控系统

  **5.基于计算机视觉的颜色矩阵识别系统**
- 设计并实现了一套完整的颜色矩阵识别算法，能够从复杂背景中精确定位、提取和识别4×4彩色矩阵，应用于工业质检和色彩标定场景
- 开发了基于HSV色彩空间的鲁棒颜色识别方法，通过自适应阈值和形态学处理，实现对红、绿、蓝、黄等多种颜色的准确分类，有效应对不同光照条件
- 实现了基于四点透视变换的图像校正算法，通过黑色标记点自动检测和距离约束筛选，解决了视角倾斜和畸变问题，提高了识别准确率
- 采用区域采样和频率统计的方法提高颜色识别的鲁棒性，系统可批量处理图像并自动生成结构化输出，实现了从图像采集到结果输出的全流程自动化

## AI工具与行业研究经验

- **AI产品使用经验**：深度使用ChatGPT、Gemini、Claude等大语言模型，熟练掌握提示工程技术，应用于学术研究、代码开发和数据分析场景
- **AI行业研究**：持续关注并分析全球AI发展趋势，包括多模态大模型、AI Agent和生成式AI在垂直领域的应用，定期阅读ArXiv、Hugging Face和Google AI Blog等国际AI研究平台

## 研究兴趣关注AI在医疗健康和智能交通领域的应用与发展趋势

- 对多模态大模型与Agent技术有持续研究，了解最新技术进展
- 积极探索AI技术在国际市场的商业化路径与挑战，特别关注AI技术如何赋能传统行业转型

## 技能/其他

- **语言**：英语CET6，流利的听说读写能力，能够无障碍阅读英文技术文档
- **人工智能与机器学习**

  - **框架：** PyTorch
  - **领域：** 计算机视觉、深度学习
  - **技术：** CNN、注意力机制、迁移学习
  - **库：** NumPy、Pandas、SciPy
  - **可视化：** Matplotlib、Seaborn、Plotly
- **软技能**

  - 优秀的团队协作与项目管理能力，在志愿者协会管理中培养的领导力和组织能力
  - 出色的市场分析与研究能力，善于收集和整理信息，形成有价值的洞察
  - 良好的沟通表达能力，能够清晰地呈现复杂技术概念和研究发现
