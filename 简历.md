# 个人简历

## 教育背景

- **萨塞克斯大学- 人工智能与自适应系统专业-硕士**  2024年9月 – 2026年3月
  - **核心课程：**图像处理、自然语言处理、机器学习、动物与机器智能、智能系统技术、Python编程
  - **毕业论文方向：**基于 U-Net + VAE 的医学图像异常检测模型研究，关注模型不确定性估计与实际临床落地的适配性
- **烟台大学-智能科学与技术专业- 学士**  2020年9月 – 2024年6月
  - **平均绩点：** 3.21/5.0（83.84/100）
  - **毕业论文：**基于深度学习的用户表情识别系统
  - **相关课程：**数据结构与算法、模式识别、图像处理、高级编程语言（R语言）、智能机器人、问题解决策略
  - **校内经历：**担任蓝翼青年志愿者协会副会长，校内校外组织开展数十次志愿服务活动

## 实习经历

**山东诺瑞特智能科技有限公司**  **软件技术开发**  2023.07 - 2023.08

- 参与公司产品智能热水器控制器的软件开发
- 主动提出并设计语音控制功能原型，构建初步命令识别机制，有效提升人机交互效率
- 协助撰写项目功能需求文档并参与方案评审，提升团队软硬件协同能力

## 项目经历与获奖情况

**1.Kaggle竞赛Image Matching Challenge 2025 铜牌区**  排名:54 / 904（前1.6%）

- 参与团队竞赛，学习图像匹配技术，使用预训练模型进行特征提取和图像配准
- 负责数据预处理和结果分析，实践了图像数据的标准化处理和模型输出解析
- 通过竞赛深入了解计算机视觉流程，获得了团队协作和项目管理经验

  **2.Kaggle竞赛Yale/UNC-CH - Geophysical Waveform Inversion 铜牌区**  排名:72/ 719（前6.5%）
- 参与团队竞赛，使用UNet架构进行图像分割任务，学习了编码器-解码器结构的应用
- 负责数据增强模块，实现图像翻转、随机裁剪和噪声添加等数据处理技术
- 通过实践掌握了深度学习模型训练流程，包括损失函数设计和模型优化方法

  **3.Kaggle竞赛Predict Podcast Listening Time**  排名:148 / 3310（前5%）
- 参与用户行为预测回归任务，处理包含缺失值和类别变量的真实业务数据集
- 使用CatBoost和LightGBM等梯度提升算法，实践特征工程和交叉验证技术
- 学习模型集成策略，通过组合多个模型提升预测准确率，获得推荐系统相关经验

  **4.基于深度学习的车牌识别系统**
- 使用PyTorch框架构建CNN卷积神经网络模型，实现中国车牌字符的精准识别，模型包含多层卷积层、批归一化层和池化层，有效提取车牌图像特征
- 设计数据预处理流程，包括图像尺寸统一化(224×224)、张量转换和标准化，提高模型对不同光照和角度下车牌图像的适应性
- 实现自定义数据集类，支持高效批量处理和数据增强，采用交叉熵损失函数和Adam优化器，通过30轮迭代训练将损失从0.07降至0.025
- 应用批归一化技术提升模型收敛速度和稳定性，最终模型在测试集上展现出良好的泛化能力，可应用于智能交通和安防监控系统

  **5.基于计算机视觉的颜色矩阵识别系统**
- 设计并实现了一套完整的颜色矩阵识别算法，能够从复杂背景中精确定位、提取和识别4×4彩色矩阵，应用于工业质检和色彩标定场景
- 开发了基于HSV色彩空间的鲁棒颜色识别方法，通过自适应阈值和形态学处理，实现对红、绿、蓝、黄等多种颜色的准确分类，有效应对不同光照条件
- 实现了基于四点透视变换的图像校正算法，通过黑色标记点自动检测和距离约束筛选，解决了视角倾斜和畸变问题，提高了识别准确率
- 采用区域采样和频率统计的方法提高颜色识别的鲁棒性，系统可批量处理图像并自动生成结构化输出，实现了从图像采集到结果输出的全流程自动化

## 研究兴趣

- 关注机器学习算法在实际业务场景中的应用，特别是推荐系统和搜索排序
- 对深度学习模型优化和工程化部署有浓厚兴趣
- 积极学习大规模数据处理和算法优化技术

## 技能/其他

- **编程语言**：Python（熟练）、C++（基础）、Java（了解）、R语言（熟悉）
- **机器学习与深度学习**

  - **框架：** PyTorch（熟悉）、TensorFlow（了解）
  - **算法：** CNN、梯度提升算法（CatBoost、LightGBM、XGBoost）
  - **领域：** 计算机视觉、数据分析、推荐系统
  - **数据处理：** NumPy、Pandas、SciPy、Scikit-learn
  - **可视化：** Matplotlib、Seaborn、Plotly
- **数据科学与统计**

  - 熟悉概率论、数理统计、线性代数等数学基础
  - 具备良好的数据敏感度和逻辑分析能力
  - 擅长特征工程、数据清洗和探索性数据分析
- **开发环境与工具**

  - **操作系统：** Windows、Linux（基础）
  - **开发工具：** Jupyter Notebook、PyCharm、Git（基础）
  - **平台：** 熟悉Kaggle竞赛平台
- **语言能力**：英语（CET6，能够阅读英文技术文档）
- **软技能**

  - 优秀的团队协作与沟通能力，具备良好的学习适应能力
  - 强烈的技术热情和好奇心，善于独立思考和问题解决
  - 具备数据敏感度和逻辑分析能力，能够从数据中提取有价值的洞察
