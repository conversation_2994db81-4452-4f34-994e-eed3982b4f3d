# 个人简历

## 教育背景

- **萨塞克斯大学- 人工智能与自适应系统专业-硕士**  2024年9月 – 2026年3月
  - **核心课程：**图像处理、自然语言处理、机器学习、动物与机器智能、智能系统技术、Python编程
  - **毕业论文方向：**基于 U-Net + VAE 的医学图像异常检测模型研究，关注模型不确定性估计与实际临床落地的适配性
- **烟台大学-智能科学与技术专业- 学士**  2020年9月 – 2024年6月
  - **平均绩点：** 3.21/5.0（83.84/100）
  - **毕业论文：**基于深度学习的用户表情识别系统
  - **相关课程：**数据结构与算法、模式识别、图像处理、高级编程语言（R语言）、智能机器人、问题解决策略
  - **校内经历：**担任蓝翼青年志愿者协会副会长，校内校外组织开展数十次志愿服务活动

## 实习经历

**山东诺瑞特智能科技有限公司**  **软件技术开发**  2023.07 - 2023.08

- 参与公司产品智能热水器控制器的软件开发
- 主动提出并设计语音控制功能原型，构建初步命令识别机制，有效提升人机交互效率
- 协助撰写项目功能需求文档并参与方案评审，提升团队软硬件协同能力

## 项目经历与获奖情况

**1.Kaggle竞赛Image Matching Challenge 2025 银牌区**  排名:14 / 842（前1.6%）

- 构建基于全深度学习图像匹配流程，整合了DINO-ViT（全局特征提取）、ALIKE（局部描述子）和LightGlue（基于注意力机制的匹配器），实现关键点提取与图像配准。
- 将预训练的DINO与ALIKE模型集成至LightGlue匹配框架中，并进行模型参数调优与多分辨率匹配优化，提升系统在大规模图像对中的处理效率和精度。
- 项目聚焦AI视觉算法在多视角图像理解场景中的落地能力，可扩展至AR导航、自动驾驶领域

  **2.Kaggle竞赛Yale/UNC-CH - Geophysical Waveform Inversion 银牌区**  排名:43 / 658（前6.5%）
- 采用UNet架构作为基础模型，对下采样层通道数和激活函数结构进行优化，提升模型对速度图中边界区域与细节层次的建模能力。
- 利用对称地震波形的物理特性设计数据增强策略，包括通道维度翻转（reverse along channel）、随机裁剪、噪声注入等方法，有效提升模型的泛化能力。
- 实施混合精度训练（float16）以降低显存占用、加快训练速度，提升中低端GPU环境下模型性能表现；同时融合梯度裁剪（Gradient Clipping）与EarlyStopping防止梯度爆炸与过拟合。

  **3.Kaggle竞赛Predict Podcast Listening Time**  排名:148 / 3310（前5%）
- 本任务为有偏分布的回归问题，目标是预测用户收听播客节目的时长，RMSE为主要评估指标。分析发现原始数据存在大量缺失值、类别变量及偏态特征，采用均值与频率填充处理缺失，结合目标编码与频率编码提升离散特征可学习性，对 `duration`与 `user_age`等偏态特征进行对数变换以缓解长尾效应。
- 构建多个CatBoost模型，充分利用其对类别特征的原生支持，避免信息损失。通过分组K折交叉验证避免用户重叠，提升泛化能力。手动调参优化 `depth`、`l2_leaf_reg`、`learning_rate`等关键超参，设计加权集成策略融合CatBoost与LightGBM模型，提升鲁棒性。最终在验证集上实现RMSE 13.12、MAE 9.47，较Baseline提升约10%。

  **4.基于深度学习的车牌识别系统**
- 使用PyTorch框架构建CNN卷积神经网络模型，实现中国车牌字符的精准识别，模型包含多层卷积层、批归一化层和池化层，有效提取车牌图像特征
- 设计数据预处理流程，包括图像尺寸统一化(224×224)、张量转换和标准化，提高模型对不同光照和角度下车牌图像的适应性
- 实现自定义数据集类，支持高效批量处理和数据增强，采用交叉熵损失函数和Adam优化器，通过30轮迭代训练将损失从0.07降至0.025
- 应用批归一化技术提升模型收敛速度和稳定性，最终模型在测试集上展现出良好的泛化能力，可应用于智能交通和安防监控系统

  **5.基于计算机视觉的颜色矩阵识别系统**
- 设计并实现了一套完整的颜色矩阵识别算法，能够从复杂背景中精确定位、提取和识别4×4彩色矩阵，应用于工业质检和色彩标定场景
- 开发了基于HSV色彩空间的鲁棒颜色识别方法，通过自适应阈值和形态学处理，实现对红、绿、蓝、黄等多种颜色的准确分类，有效应对不同光照条件
- 实现了基于四点透视变换的图像校正算法，通过黑色标记点自动检测和距离约束筛选，解决了视角倾斜和畸变问题，提高了识别准确率
- 采用区域采样和频率统计的方法提高颜色识别的鲁棒性，系统可批量处理图像并自动生成结构化输出，实现了从图像采集到结果输出的全流程自动化

## AI工具与行业研究经验

- **AI产品使用经验**：深度使用ChatGPT、Gemini、Claude等大语言模型，熟练掌握提示工程技术，应用于学术研究、代码开发和数据分析场景
- **AI行业研究**：持续关注并分析全球AI发展趋势，包括多模态大模型、AI Agent和生成式AI在垂直领域的应用，定期阅读ArXiv、Hugging Face和Google AI Blog等国际AI研究平台

## 研究兴趣关注AI在医疗健康和智能交通领域的应用与发展趋势

- 对多模态大模型与Agent技术有持续研究，了解最新技术进展
- 积极探索AI技术在国际市场的商业化路径与挑战，特别关注AI技术如何赋能传统行业转型

## 技能/其他

- **语言**：英语CET6，流利的听说读写能力，能够无障碍阅读英文技术文档
- **人工智能与机器学习**

  - **框架：** PyTorch
  - **领域：** 计算机视觉、深度学习
  - **技术：** CNN、注意力机制、迁移学习
  - **库：** NumPy、Pandas、SciPy
  - **可视化：** Matplotlib、Seaborn、Plotly
- **软技能**

  - 优秀的团队协作与项目管理能力，在志愿者协会管理中培养的领导力和组织能力
  - 出色的市场分析与研究能力，善于收集和整理信息，形成有价值的洞察
  - 良好的沟通表达能力，能够清晰地呈现复杂技术概念和研究发现
