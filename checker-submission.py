import pygame
import copy
from tkinter import messagebox, Tk

# Start pygame and hide tkinter window for popups
pygame.init()
tk_root = Tk()
tk_root.withdraw()

# Set up game board size
WIDTH, HEIGHT = 800, 800  # How big the board is
ROWS, COLS = 8, 8
SQUARE_SIZE = WIDTH // COLS
MENU_WIDTH = 200  # Side menu width
TOTAL_WIDTH = WIDTH + MENU_WIDTH  # Total screen width

# Game colors
RED = (255, 0, 0)
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
GREY = (128, 128, 128)
BLUE = (0, 0, 255)
GREEN = (0, 255, 0)
LIGHT_GREY = (200, 200, 200)

# Load crown image for kings
CROWN = pygame.transform.scale(pygame.image.load('D:\Checkers-Python-main\Checkers-Python-main\crown1.png'), (44, 25))

# Make game window
WIN = pygame.display.set_mode((TOTAL_WIDTH, HEIGHT))
pygame.display.set_caption('Checkers with AI')

# Button class for menu items
# Makes buttons that change color when mouse hovers over them
class Button:
    def __init__(self, x, y, width, height, text, color):
        self.rect = pygame.Rect(x, y, width, height)
        self.text = text
        self.color = color
        self.is_hovered = False

    def draw(self, win):
        color = LIGHT_GREY if self.is_hovered else self.color
        pygame.draw.rect(win, color, self.rect)
        pygame.draw.rect(win, BLACK, self.rect, 2)

        # Render text
        font = pygame.font.SysFont('arial', 24)
        text = font.render(self.text, True, BLACK)
        text_rect = text.get_rect(center=self.rect.center)
        win.blit(text, text_rect)

    def handle_event(self, event):
        if event.type == pygame.MOUSEMOTION:
            self.is_hovered = self.rect.collidepoint(event.pos)
        elif event.type == pygame.MOUSEBUTTONDOWN:
            if self.is_hovered:
                return True
        return False

# Dropdown menu for game difficulty
# Lets player pick Easy, Medium or Hard
class Dropdown:
    def __init__(self, x, y, width, height):
        self.rect = pygame.Rect(x, y, width, height)
        self.options = ['Easy', 'Medium', 'Hard']
        self.selected = 'Medium'
        self.is_active = False
        self.option_rects = []

        for i in range(len(self.options)):
            self.option_rects.append(pygame.Rect(x, y + height * (i+1), width, height))

    def draw(self, win):
        # Draw main button
        pygame.draw.rect(win, WHITE, self.rect)
        pygame.draw.rect(win, BLACK, self.rect, 2)

        # Render selected text
        font = pygame.font.SysFont('arial', 24)
        text = font.render(self.selected, True, BLACK)
        text_rect = text.get_rect(center=self.rect.center)
        win.blit(text, text_rect)

        # Draw dropdown if active
        if self.is_active:
            for i, option in enumerate(self.options):
                rect = self.option_rects[i]
                pygame.draw.rect(win, WHITE, rect)
                pygame.draw.rect(win, BLACK, rect, 2)
                text = font.render(option, True, BLACK)
                text_rect = text.get_rect(center=rect.center)
                win.blit(text, text_rect)

    def handle_event(self, event):
        if event.type == pygame.MOUSEBUTTONDOWN:
            if self.rect.collidepoint(event.pos):
                self.is_active = not self.is_active
            elif self.is_active:
                for i, rect in enumerate(self.option_rects):
                    if rect.collidepoint(event.pos):
                        self.selected = self.options[i]
                        self.is_active = False
                        return True
        return False

# Checker piece class
# Keeps track of where pieces are and if they're kings
class Piece:
    PADDING = 15
    OUTLINE = 2

    def __init__(self, row, col, color):
        self.row = row
        self.col = col
        self.color = color
        self.king = False
        self.x = 0
        self.y = 0
        self.calc_pos()

    def calc_pos(self):
        self.x = SQUARE_SIZE * self.col + SQUARE_SIZE // 2
        self.y = SQUARE_SIZE * self.row + SQUARE_SIZE // 2

    def make_king(self):
        self.king = True

    def draw(self, win):
        radius = SQUARE_SIZE//2 - self.PADDING
        pygame.draw.circle(win, GREY, (self.x, self.y), radius + self.OUTLINE)
        pygame.draw.circle(win, self.color, (self.x, self.y), radius)
        if self.king:
            win.blit(CROWN, (self.x - CROWN.get_width()//2, self.y - CROWN.get_height()//2))

    def move(self, row, col):
        self.row = row
        self.col = col
        self.calc_pos()

    def __repr__(self):
        return str(self.color)

# Game board class
# Handles all the game rules and keeps track of pieces
class Board:
    def __init__(self):
        self.board = []
        self.selected = None
        self.red_left = self.black_left = 12
        self.red_kings = self.black_kings = 0
        self.turn = RED
        self.create_board()

    def draw_squares(self, win):
        win.fill(BLACK)
        for row in range(ROWS):
            for col in range(row % 2, COLS, 2):
                pygame.draw.rect(win, WHITE, (col * SQUARE_SIZE, row * SQUARE_SIZE, SQUARE_SIZE, SQUARE_SIZE))

    def promote_to_king(self, piece):
        if not piece.king:
            piece.make_king()
            if piece.color == RED:
                self.red_kings += 1
            else:
                self.black_kings += 1

    def move(self, piece, row, col):
        self.board[piece.row][piece.col], self.board[row][col] = self.board[row][col], self.board[piece.row][piece.col]
        piece.move(row, col)

        # Check for king promotion at baseline
        if row == ROWS - 1 or row == 0:
            self.promote_to_king(piece)

    def get_piece(self, row, col):
        return self.board[row][col]

    def create_board(self):
        for row in range(ROWS):
            self.board.append([])
            for col in range(COLS):
                if col % 2 == ((row + 1) % 2):
                    if row < 3:
                        self.board[row].append(Piece(row, col, BLACK))
                    elif row > 4:
                        self.board[row].append(Piece(row, col, RED))
                    else:
                        self.board[row].append(0)
                else:
                    self.board[row].append(0)

    def draw(self, win):
        self.draw_squares(win)
        for row in range(ROWS):
            for col in range(COLS):
                piece = self.board[row][col]
                if piece != 0:
                    piece.draw(win)

    def remove(self, pieces):
        for piece in pieces:
            self.board[piece.row][piece.col] = 0
            if piece != 0:
                if piece.color == RED:
                    self.red_left -= 1
                else:
                    self.black_left -= 1

    def winner(self):
        # Check if any side has no pieces left
        if self.red_left <= 0:
            return 'BLACK'
        elif self.black_left <= 0:
            return 'RED'

        # Check if any side has no valid moves
        red_has_moves = False
        black_has_moves = False

        for row in range(ROWS):
            for col in range(COLS):
                piece = self.board[row][col]
                if piece != 0:
                    moves = self.get_valid_moves(piece)
                    if moves:  # If there are any valid moves
                        if piece.color == RED:
                            red_has_moves = True
                        else:
                            black_has_moves = True

        if not red_has_moves:
            return 'BLACK'
        elif not black_has_moves:
            return 'RED'

        return None

    def get_valid_moves(self, piece):
        """Find where a piece can move
        Checks both normal moves and jumps
        Only shows normal moves if no jumps are possible
        Returns a dictionary of possible moves"""
        moves = {}      # Regular moves: (row,col) -> []
        captures = {}   # Capture moves: (row,col) -> [captured pieces]

        # Check capture opportunities in each direction
        # Kings can move in all 4 directions, regular pieces only forward
        if piece.king:
            directions = [(-1, -1), (-1, 1), (1, -1), (1, 1)]  # All diagonals for kings
        else:
            if piece.color == RED:
                directions = [(-1, -1), (-1, 1)]  # Red moves up (rows decrease)
            else:
                directions = [(1, -1), (1, 1)]    # Black moves down (rows increase)

        # Check for captures
        for dr, dc in directions:
            row = piece.row + dr
            col = piece.col + dc
            # Make sure we're within the board
            if 0 <= row < ROWS and 0 <= col < COLS:
                target = self.board[row][col]
                # If there's an opponent's piece
                if target != 0 and target.color != piece.color:
                    jump_row = row + dr
                    jump_col = col + dc
                    # Check if we can jump over it
                    if 0 <= jump_row < ROWS and 0 <= jump_col < COLS:
                        if self.board[jump_row][jump_col] == 0:  # Make sure landing square is empty
                            captures[(jump_row, jump_col)] = [target]  # Capture just this one piece

        # If captures are available, only return capture moves
        if captures:
            return captures

        # Otherwise return regular moves
        for dr, dc in directions:
            row = piece.row + dr
            col = piece.col + dc
            if 0 <= row < ROWS and 0 <= col < COLS:
                if self.board[row][col] == 0:  # Can only move to empty squares
                    moves[(row, col)] = []

        return moves

    def get_all_capture_paths(self, piece):
        """Find all possible jump sequences
        Looks for multiple jumps in a row
        Tests different paths on fake boards
        Returns all possible ending spots"""

        all_paths = {}  # Dictionary to store all possible capture paths

        # Get initial capture moves
        initial_captures = self.get_valid_moves(piece)

        # If no captures available, return empty dictionary
        if not any(initial_captures.values()):
            return {}

        # For each initial capture, find all possible continuation paths
        for move, captured in initial_captures.items():
            if captured:  # If this is a capture move
                # Create a temporary board to simulate this capture
                temp_board = copy.deepcopy(self)
                temp_piece = temp_board.get_piece(piece.row, piece.col)

                # Make the move on the temporary board
                temp_board.move(temp_piece, move[0], move[1])
                temp_board.remove(captured)

                # Check for additional captures from the new position
                next_paths = temp_board.find_capture_continuations(temp_piece, [captured[0]])

                if next_paths:  # If there are continuation paths
                    # Add all continuation paths to our results
                    for end_pos, path_captures in next_paths.items():
                        all_paths[end_pos] = path_captures
                else:
                    # If no continuations, this is a final position
                    all_paths[move] = captured

        return all_paths

    def find_capture_continuations(self, piece, captured_so_far):
        """Find more jumps after the first one
        Checks if a piece can keep jumping
        Builds all possible jump paths
        Returns all possible ending spots"""

        continuations = {}

        # Get next possible captures
        next_captures = self.get_valid_moves(piece)

        # If no more captures, this path ends
        if not any(next_captures.values()):
            return {}

        # For each possible next capture
        for move, new_captured in next_captures.items():
            if new_captured:  # If this is a capture move
                # Create a temporary board to simulate this capture
                temp_board = copy.deepcopy(self)
                temp_piece = temp_board.get_piece(piece.row, piece.col)

                # Make the move on the temporary board
                temp_board.move(temp_piece, move[0], move[1])
                temp_board.remove(new_captured)

                # Update the list of captured pieces for this path
                path_captures = captured_so_far + new_captured

                # Check for further captures
                further_paths = temp_board.find_capture_continuations(temp_piece, path_captures)

                if further_paths:  # If there are further continuation paths
                    # Add all further paths to our results
                    for end_pos, path_captures in further_paths.items():
                        continuations[end_pos] = path_captures
                else:
                    # If no further continuations, this is a final position
                    continuations[move] = path_captures

        return continuations

    def has_any_captures(self, color):
        """Check if any piece can jump
        Looks at all pieces of one color
        Used because jumps are required in checkers
        Returns True if jumps exist, False if not"""

        for row in range(ROWS):
            for col in range(COLS):
                piece = self.board[row][col]
                if piece != 0 and piece.color == color:
                    moves = self.get_valid_moves(piece)
                    if any(moves):  # If there are any moves
                        if any(skip for skip in moves.values()):  # If any move involves capturing
                            return True
        return False

# AI player functions
# Uses minimax to find good moves
# Looks ahead to pick the best option
def minimax(board, depth, max_player, alpha, beta):
    """Find the best move for AI
    Uses minimax with alpha-beta to save time
    Looks ahead several moves
    Returns (score, best move)"""

    # Check if we've reached the end of our search
    # Stop if someone has won or we've reached our max search depth
    winner = board.winner()
    if depth == 0 or winner:
        return evaluate(board), board

    if max_player:
        # AI's turn (BLACK) - trying to get highest possible score
        max_eval = float('-inf')  # Start with lowest possible score
        best_move = None  # No best move found yet

        # Try each possible move
        for move in get_all_moves(board, BLACK):
            # See how good this move is by checking future moves
            evaluation = minimax(move, depth - 1, False, alpha, beta)[0]

            # If this is better than our best so far, remember it
            max_eval = max(max_eval, evaluation)
            if max_eval == evaluation:
                best_move = move

            # Alpha-beta pruning - skip checking moves that won't be chosen
            # This makes the AI much faster
            alpha = max(alpha, evaluation)
            if beta <= alpha:
                break  # Stop checking more moves, opponent won't allow this path

        return max_eval, best_move
    else:
        # Player's turn (RED) - AI assumes player will make best move for player
        # (which means worst move for AI, so minimum score)
        min_eval = float('inf')  # Start with highest possible score
        best_move = None  # No best move found yet

        # Try each possible move
        for move in get_all_moves(board, RED):
            # See how good this move is by checking future moves
            evaluation = minimax(move, depth - 1, True, alpha, beta)[0]

            # If this is better for player (worse for AI) than our best so far, remember it
            min_eval = min(min_eval, evaluation)
            if min_eval == evaluation:
                best_move = move

            # Alpha-beta pruning - skip checking moves that won't be chosen
            beta = min(beta, evaluation)
            if beta <= alpha:
                break  # Stop checking more moves, opponent won't allow this path

        return min_eval, best_move

def evaluate(board):
    """Score the board for AI
    Gives points for pieces, kings, position
    Higher score is better for AI (BLACK)
    Lower score is better for player (RED)"""

    # Basic piece count - more AI pieces is good, more player pieces is bad
    # Positive score means good for AI (BLACK), negative means good for player (RED)
    piece_score = board.black_left - board.red_left

    # Kings are worth more than regular pieces (50% more)
    king_score = (board.black_kings * 0.5 - board.red_kings * 0.5)

    # Position score - pieces in better positions get bonus points
    position_score = 0
    for row in range(ROWS):
        for col in range(COLS):
            piece = board.get_piece(row, col)
            if piece != 0:
                if piece.king:
                    # Kings are more valuable in the center of the board
                    # (can control more squares)
                    distance_from_center = abs(row - 3.5) + abs(col - 3.5)
                    position_value = 0.1 * (7 - distance_from_center)
                    if piece.color == BLACK:
                        position_score += position_value  # Good for AI
                    else:
                        position_score -= position_value  # Good for player
                else:
                    # Regular pieces are better when closer to becoming kings
                    if piece.color == BLACK:
                        position_score += 0.05 * row  # Black pieces want to move down
                    else:
                        position_score -= 0.05 * (7 - row)  # Red pieces want to move up

    # Bonus for having capture opportunities
    # Having options to capture is valuable
    capture_potential = 0
    for row in range(ROWS):
        for col in range(COLS):
            piece = board.get_piece(row, col)
            if piece != 0:
                moves = board.get_valid_moves(piece)
                if any(skip for skip in moves.values()):  # If captures are available
                    if piece.color == BLACK:
                        capture_potential += 0.2  # Good for AI
                    else:
                        capture_potential -= 0.2  # Good for player

    # Combine all factors to get final score
    return piece_score + king_score + position_score + capture_potential

def get_all_moves(board, color):
    """Find all possible moves for one color
    Checks all pieces of that color
    Jumps come first (they're required)
    Returns list of boards after each move"""

    moves = []      # List to store boards after regular moves
    captures = []   # List to store boards after capture moves

    # First check for any captures available (captures are mandatory in checkers)
    for row in range(ROWS):
        for col in range(COLS):
            piece = board.get_piece(row, col)
            # If there's a piece of the right color at this position
            if piece != 0 and piece.color == color:
                # Get all valid moves for this piece
                valid_moves = board.get_valid_moves(piece)
                for move, skip in valid_moves.items():
                    # If this move involves capturing (skip list is not empty)
                    if skip:
                        # Create a copy of the board to simulate making this move
                        temp_board = copy.deepcopy(board)
                        # Get the same piece in the copied board
                        temp_piece = temp_board.get_piece(row, col)
                        # Make the move in the copied board
                        temp_board.move(temp_piece, move[0], move[1])
                        # Remove the captured pieces
                        temp_board.remove(skip)
                        # Add the resulting board to our captures list
                        captures.append(temp_board)

    # If there are any captures, only return those (since captures are mandatory)
    # This ensures the AI follows the rule that captures must be made when available
    if captures:
        return captures

    # If no captures are available, check all regular moves
    for row in range(ROWS):
        for col in range(COLS):
            piece = board.get_piece(row, col)
            if piece != 0 and piece.color == color:
                valid_moves = board.get_valid_moves(piece)
                for move, skip in valid_moves.items():
                    # Create a copy of the board to simulate this move
                    temp_board = copy.deepcopy(board)
                    temp_piece = temp_board.get_piece(row, col)
                    # Make the move in the copied board
                    temp_board.move(temp_piece, move[0], move[1])
                    # For regular moves, skip will be an empty list
                    temp_board.remove(skip)
                    # Add the resulting board to our moves list
                    moves.append(temp_board)

    # Return all possible regular moves
    return moves

# Drawing functions
# Show the game on screen
def draw_valid_moves(win, moves):
    """Show where pieces can move
    Draws green circles on valid spots
    Helps player see possible moves"""

    for move in moves:
        row, col = move
        pygame.draw.circle(win, GREEN, (col * SQUARE_SIZE + SQUARE_SIZE // 2, row * SQUARE_SIZE + SQUARE_SIZE // 2), 15)

def draw_help(win, board):
    """Show which pieces can jump
    Puts yellow boxes around pieces that can capture
    Only shows for player's pieces (red)"""

    for row in range(ROWS):
        for col in range(COLS):
            piece = board.get_piece(row, col)
            if piece != 0 and piece.color == RED:  # Only show for player's pieces
                moves = board.get_valid_moves(piece)
                # Only show yellow border if there are captures available
                if moves and any(skip for skip in moves.values()):  # If any move involves capturing
                    pygame.draw.rect(win, (255, 255, 0, 128),
                                   (col * SQUARE_SIZE, row * SQUARE_SIZE,
                                    SQUARE_SIZE, SQUARE_SIZE), 3)

def show_rules():
    """Show game rules in a popup
    Explains how to play checkers
    Lists the basic rules"""

    rules = """
    Checkers Rules:
    1. Pieces move diagonally forward
    2. Captures are mandatory
    3. Kings can move both forward and backward
    4. Multiple captures in one turn are allowed
    5. A piece becomes a king when reaching the opposite end
    6. If a normal piece captures a king, it becomes a king
    """
    messagebox.showinfo("Rules", rules)

def draw_menu(win, restart_btn, difficulty_dd, help_btn, turn_text):
    """Draw side menu
    Shows buttons and game info
    Tells player whose turn it is"""

    # Draw menu background
    pygame.draw.rect(win, WHITE, (WIDTH, 0, MENU_WIDTH, HEIGHT))
    pygame.draw.line(win, BLACK, (WIDTH, 0), (WIDTH, HEIGHT), 2)

    # Draw buttons and dropdown
    restart_btn.draw(win)
    difficulty_dd.draw(win)
    help_btn.draw(win)

    # Draw turn status box (moved further down)
    status_box_rect = pygame.Rect(WIDTH + 25, 400, 150, 40)  # Moved down to y=400
    pygame.draw.rect(win, WHITE, status_box_rect)
    pygame.draw.rect(win, BLACK, status_box_rect, 2)

    # Render turn status text in red
    font = pygame.font.SysFont('arial', 20)
    text = font.render(turn_text, True, RED)
    text_rect = text.get_rect(center=status_box_rect.center)
    win.blit(text, text_rect)

def main():
    """Main game loop
    Sets up the game and runs it
    Handles player moves and AI turns"""

    board = Board()
    run = True
    clock = pygame.time.Clock()
    selected = None
    valid_moves = {}
    show_help = True  # Help mode is on by default
    turn_text = "Your Turn"  # Initialize turn status text

    # Create menu buttons
    button_width = 150
    button_height = 40
    padding = 25  # Reduced padding for vertical layout

    restart_btn = Button(WIDTH + padding, 50, button_width, button_height, "Restart", WHITE)
    help_btn = Button(WIDTH + padding, 120, button_width, button_height, "Help", WHITE)
    difficulty_dd = Dropdown(WIDTH + padding, 190, button_width, button_height)

    # Set initial AI depth based on default difficulty
    ai_depth = 5  # Medium difficulty

    while run:
        clock.tick(60)

        winner = board.winner()
        if winner:
            messagebox.showinfo("Game Over", f"{winner} wins!")
            run = False
            continue

        if board.turn == BLACK:
            turn_text = "AI is thinking..."
            # Force a display update to show the "thinking" message
            WIN.fill(WHITE)
            board_surface = pygame.Surface((WIDTH, HEIGHT))
            board.draw(board_surface)
            WIN.blit(board_surface, (0, 0))
            draw_menu(WIN, restart_btn, difficulty_dd, help_btn, turn_text)
            pygame.display.update()

            _, new_board = minimax(board, ai_depth, True, float('-inf'), float('inf'))
            board = new_board
            board.turn = RED
            turn_text = "Your Turn"

        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                run = False

            # Handle menu events
            if restart_btn.handle_event(event):
                board = Board()
                selected = None
                valid_moves = {}
                turn_text = "Your Turn"
                continue

            if difficulty_dd.handle_event(event):
                if difficulty_dd.selected == 'Easy':
                    ai_depth = 2
                elif difficulty_dd.selected == 'Medium':
                    ai_depth = 5
                else:  # Hard
                    ai_depth = 7
                continue

            if help_btn.handle_event(event):
                show_rules()
                continue

            if event.type == pygame.MOUSEBUTTONDOWN:
                pos = pygame.mouse.get_pos()
                # Only handle board clicks within the board area
                if pos[0] < WIDTH:
                    row = pos[1] // SQUARE_SIZE
                    col = pos[0] // SQUARE_SIZE
                    piece = board.get_piece(row, col)

                    if selected:
                        result = move(board, selected, row, col, valid_moves)
                        if not result:
                            messagebox.showwarning("Invalid Move", "You can't move there!")
                            selected = None
                            valid_moves = {}
                        else:
                            selected = None
                            valid_moves = {}
                    elif piece != 0 and piece.color == RED:
                        selected = piece
                        # Check if there are any captures available for this color
                        has_captures = board.has_any_captures(RED)

                        if has_captures:
                            # If captures are available, check if this piece can capture
                            piece_captures = board.get_valid_moves(piece)
                            if any(piece_captures.values()):
                                # If this piece can capture, show all possible capture paths
                                valid_moves = board.get_all_capture_paths(piece)
                            else:
                                # This piece can't capture, but captures are mandatory
                                valid_moves = {}
                        else:
                            # No captures available, show regular moves
                            valid_moves = board.get_valid_moves(piece)

        # Draw everything
        WIN.fill(WHITE)  # Clear screen

        # Draw board
        board_surface = pygame.Surface((WIDTH, HEIGHT))
        board.draw(board_surface)
        if selected:
            pygame.draw.rect(board_surface, BLUE,
                           (selected.col * SQUARE_SIZE, selected.row * SQUARE_SIZE, SQUARE_SIZE, SQUARE_SIZE), 4)
            draw_valid_moves(board_surface, valid_moves.keys())
        if show_help:
            draw_help(board_surface, board)

        WIN.blit(board_surface, (0, 0))

        # Draw menu with current turn status
        draw_menu(WIN, restart_btn, difficulty_dd, help_btn, turn_text)

        pygame.display.update()

    pygame.quit()

def move(board, piece, row, col, valid_moves):
    """Move a piece on the board
    Checks if move is allowed
    Handles jumps and making kings
    Returns True if move worked, False if not"""

    # Check if the square clicked is in the valid moves for this piece
    if (row, col) in valid_moves:
        # Check if any piece of this color can capture an opponent's piece
        has_captures = board.has_any_captures(piece.color)

        # If captures are available but this move is not a capture, it's invalid
        # In checkers, capturing is mandatory when possible
        if has_captures and not valid_moves[(row, col)]:
            messagebox.showwarning("Invalid Move", "You must capture when a capture is available!")
            return False

        # Move the piece to the new position
        board.move(piece, row, col)
        skipped = valid_moves[(row, col)]  # List of pieces that were jumped over (captured)

        # If we captured any pieces
        if skipped:
            # Remove the captured pieces from the board
            board.remove(skipped)

            # Special rule: If a piece captures a king, it becomes a king too
            if any(p.king for p in skipped):
                board.promote_to_king(piece)
                board.turn = BLACK  # End turn
                return True

            # Check for additional captures (multiple jumps in one turn)
            # This is where multiple captures are implemented
            additional_moves = board.get_valid_moves(piece)
            if additional_moves and any(additional_moves.values()):
                return True  # Don't end the turn yet, allowing the player to make another capture

        # If no more captures or we didn't capture, check for king promotion
        # A piece becomes a king when it reaches the opposite end of the board
        if row == ROWS - 1 or row == 0:
            board.promote_to_king(piece)

        # End player's turn, switch to AI
        board.turn = BLACK
        return True
    return False

if __name__ == '__main__':
    main()