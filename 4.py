import cv2
import numpy as np
import os
from math import sqrt

# 标准颜色定义（HSV范围）
COLOR_RANGES = {
    "red": ([0, 100, 100], [10, 255, 255], [170, 100, 100], [180, 255, 255]),
    "green": ([40, 50, 50], [80, 255, 255]),
    "blue": ([100, 50, 50], [140, 255, 255]),
    "yellow": ([20, 100, 100], [30, 255, 255])
}


def perspective_transform(img, markers):
    """四点透视变换，以上面两个定位点方向为横向方向"""
    # 将标记点分为上部和下部
    markers_sorted = sorted(markers, key=lambda x: x[1])  # 按y坐标排序
    top_markers = sorted(markers_sorted[:2], key=lambda x: x[0])  # 上面两个点按x坐标排序
    bottom_markers = sorted(markers_sorted[2:], key=lambda x: x[0])  # 下面两个点按x坐标排序

    # 重新组合为左上、右上、左下、右下顺序
    ordered_markers = [top_markers[0], top_markers[1], bottom_markers[0], bottom_markers[1]]

    # 计算目标尺寸（基于上面两个点的方向）
    width = int(sqrt((ordered_markers[1][0] - ordered_markers[0][0])  **  2 +
                                                                            (ordered_markers[1][1] - ordered_markers[0][
                                                                                1])  **  2))

    # 计算高度（取左右两侧高度的平均值）
    left_height = int(sqrt((ordered_markers[2][0] - ordered_markers[0][0])  **  2 +
                                                                                  (ordered_markers[2][1] -
                                                                                   ordered_markers[0][1])  **  2))
    right_height = int(sqrt((ordered_markers[3][0] - ordered_markers[1][0])  **  2 +
                                                                                   (ordered_markers[3][1] -
                                                                                    ordered_markers[1][1])  **  2))
    height = int((left_height + right_height) / 2)

    dst_pts = np.array([[0, 0], [width, 0], [0, height], [width, height]], dtype="float32")
    M = cv2.getPerspectiveTransform(np.array(ordered_markers, dtype="float32"), dst_pts)
    return cv2.warpPerspective(img, M, (width, height))


def detect_color(hsv_pixel):
    """识别单个像素颜色"""
    h, s, v = hsv_pixel
    if s < 30 or v < 50:  # 非彩色区域
        return None

    # 红色特殊处理（两个区间）
    if (0 <= h <= 10 and 100 <= s <= 255 and 100 <= v <= 255) or \
            (170 <= h <= 180 and 100 <= s <= 255 and 100 <= v <= 255):
        return "red"

    # 检测其他颜色
    for color, ranges in COLOR_RANGES.items():
        if color == "red":
            continue
        lower, upper = ranges
        if lower[0] <= h <= upper[0] and lower[1] <= s <= upper[1] and lower[2] <= v <= upper[2]:
            return color
    return None


def recognize_color_matrix(transformed_img):
    """识别4x4颜色矩阵"""
    hsv = cv2.cvtColor(transformed_img, cv2.COLOR_BGR2HSV)
    height, width = hsv.shape[:2]

    # 计算每个单元格区域
    cell_w, cell_h = width // 4, height // 4
    color_grid = []

    for row in range(4):
        row_colors = []
        for col in range(4):
            # 取单元格中心区域(30%面积)的均值
            x1 = col * cell_w + int(cell_w * 0.35)
            x2 = (col + 1) * cell_w - int(cell_w * 0.35)
            y1 = row * cell_h + int(cell_h * 0.35)
            y2 = (row + 1) * cell_h - int(cell_h * 0.35)

            # 统计区域内的主要颜色
            cell_region = hsv[y1:y2, x1:x2]
            colors = [detect_color(pixel) for pixel in cell_region.reshape(-1, 3)]
            valid_colors = [c for c in colors if c is not None]

            if not valid_colors:
                row_colors.append("unknown")
                continue

            # 取出现频率最高的颜色
            counts = {color: valid_colors.count(color) for color in set(valid_colors)}
            row_colors.append(max(counts, key=counts.get))
        color_grid.append(row_colors)

    return color_grid


def detect_black_markers(image_path, min_distance=100, show_process=False):
    """检测四个间距合理的最大黑色区域"""
    # 读取图像
    img = cv2.imread(image_path)
    if img is None:
        raise ValueError("无法读取图像文件")
    debug_img = img.copy()

    # 步骤1：黑色区域提取
    hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    black_mask = cv2.inRange(hsv, (0, 0, 0), (180, 255, 80))  # V<50为黑色

    # 形态学优化
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    cleaned_mask = cv2.morphologyEx(black_mask, cv2.MORPH_CLOSE, kernel, iterations=2)
    cleaned_mask = cv2.morphologyEx(cleaned_mask, cv2.MORPH_OPEN, kernel, iterations=1)

    # 步骤2：候选区域提取
    contours, _ = cv2.findContours(cleaned_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    if not contours:
        raise ValueError("未检测到任何黑色区域")

    # 计算各区域面积和中心点
    candidates = []
    for cnt in contours:
        M = cv2.moments(cnt)
        if M["m00"] == 0:
            continue
        cx = int(M["m10"] / M["m00"])
        cy = int(M["m01"] / M["m00"])
        area = cv2.contourArea(cnt)
        candidates.append({
            "center": (cx, cy),
            "area": area,
            "contour": cnt
        })

    # 按面积降序排序
    candidates.sort(key=lambda x: x["area"], reverse=True)

    # 步骤3：距离筛选
    selected = []
    for cand in candidates:
        # 跳过过小区域（面积<最大区域的5%）
        if cand["area"] < candidates[0]["area"] * 0.00002:
            continue

        # 检查与已选区域的距离
        too_close = False
        for s in selected:
            if calculate_distance(cand["center"], s["center"]) < min_distance:
                too_close = True
                break
        if not too_close:
            selected.append(cand)
            if len(selected) >= 4:
                break  # 找到四个即停止

    if len(selected) < 4:
        raise ValueError(f"仅找到 {len(selected)} 个符合间距的黑色区域")

    # 可视化
    if show_process:
        # 绘制所有候选中心（黄色）
        for cand in candidates[:20]:  # 显示前20个候选
            cv2.circle(debug_img, cand["center"], 3, (0, 255, 255), -1)

        # 绘制最终选定中心（绿色）
        for sel in selected:
            cv2.circle(debug_img, sel["center"], 8, (0, 255, 0), 2)
            cv2.drawContours(debug_img, [sel["contour"]], -1, (255, 0, 255), 1)

        # 显示距离约束
        height, width = img.shape[:2]
        cv2.putText(debug_img, f"Min Distance: {min_distance}px", (10, 30),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
        cv2.imshow("Detection Process", debug_img)
        cv2.waitKey(0)
        cv2.destroyAllWindows()

    return [s["center"] for s in selected[:4]]


def calculate_distance(p1, p2):
    """计算两点间欧氏距离"""
    return sqrt((p1[0] - p2[0])  **  2 + (p1[1] - p2[1])  **  2)


if __name__ == "__main__":
    input_folder = "D:\IP project\Photos\Photos"
    results_file = "results0502.txt"  # 结果文件名

    # 动态计算最小距离（基于图像尺寸的15%对角线）
    sample_img = cv2.imread(os.path.join(input_folder, os.listdir(input_folder)[0]))
    img_height, img_width = sample_img.shape[:2]
    diag_length = sqrt(img_width ** 2 + img_height ** 2)
    auto_min_distance = int(diag_length * 0.05)  # 动态距离阈值

    # 打开结果文件（覆盖模式）
    with open(results_file, 'w') as f:
        for filename in os.listdir(input_folder):
            if filename.lower().endswith((".png", ".jpg", ".jpeg")):
                input_path = os.path.join(input_folder, filename)

                try:
                    # 1. 定位四个标记点
                    markers = detect_black_markers(input_path,
                                                 min_distance=auto_min_distance,
                                                 show_process=True)

                    # 2. 透视校正
                    img = cv2.imread(input_path)
                    transformed = perspective_transform(img, markers)

                    # 3. 识别颜色矩阵
                    color_matrix = recognize_color_matrix(transformed)

                    # 输出到控制台（新ASCII边框格式）
                    print(f"\n文件 {filename} 识别结果：")

                    for row in color_matrix:
                        # 取每个颜色的首字母，如果是unknown则保留u
                        row_str = " ".join(f"{color[0] if color != 'unknown' else 'u'}" for color in row)
                        print(f"|  {row_str}  |")


                    # 写入结果文件（保持原格式不变）
                    f.write(f"# {filename}\n")  # 添加文件名作为注释
                    f.write('[')
                    for i, row in enumerate(color_matrix):
                        if i > 0:
                            f.write(',\n ')
                        f.write('[')
                        f.write(', '.join(f'"{color[0] if color != "unknown" else "u"}"' for color in row))
                        f.write(']')
                    f.write(']\n\n')  # 每个矩阵后加两个换行

                except Exception as e:
                    print(f"处理失败 {filename}: {str(e)}")
                    f.write(f"# {filename} 处理失败: {str(e)}\n\n")  # 记录失败信息